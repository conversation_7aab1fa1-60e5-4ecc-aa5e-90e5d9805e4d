<div id="sidebar" class="sidebar">
            <div class="sidebar-content">
                <!-- sidebar-menu  -->
                <div class="sidebar-menu">
                    <ul>
                        <li class="header-menu">
                            Categories
                        </li>
                        <li class="<% if (htmlWebpackPlugin.options.topmenu == 'db') { %>active<% } %>">
                            <a href="index.html">
                                <i class="ti-dashboard"></i>
                                <span class="menu-text">Dashboard</span>
                            </a>
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'cp') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-plug"></i>
                                <span class="menu-text">Components</span>
                                <span class="badge badge-pill badge-danger">3</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'cpdatetime') { %>class="active" <% } %>>
                                        <a href="cp_datetime.html">Date & Time</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'cptoggle') { %>class="active" <% } %>>
                                        <a href="cp_bstoggle.html">Bootstrap Toggle</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'ui') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-palette"></i>
                                <span class="menu-text">UI Elements</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uitypo') { %>class="active"<% } %>>
                                        <a href="ui_typography.html">Typography</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uicolor') { %>class="active" <% } %>>
                                        <a href="ui_colors.html">Colors</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uifa') { %>class="active"<% } %>>
                                        <a href="ui_fontawesome.html">Fontawesome Icons</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uithemify') { %>class="active"<% } %>>
                                        <a href="ui_themify.html">Themify Icons</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uibutton') { %>class="active"<% } %>>
                                        <a href="ui_buttons.html">Buttons</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uicard') { %>class="active"<% } %>>
                                        <a href="ui_cards.html">Cards</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uimodal') { %>class="active"<% } %>>
                                        <a href="ui_modals.html">Modals</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'uitoastr') { %>class="active"<% } %>>
                                        <a href="ui_toastr.html">Toastr Notifications</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'tb') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-layout-grid2"></i>
                                <span class="menu-text">Tables</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'tbbasic') { %>class="active" <% } %>>
                                        <a href="tb_basic.html">Basic Tables</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'tbdatatables') { %>class="active" <% } %>>
                                        <a href="tb_datatables.html">Datatables</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'fm') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-write"></i>
                                <span class="menu-text">Forms</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'fmcontrol') { %>class="active" <% } %>>
                                        <a href="fm_control.html">Form Control</a>
                                    </li>
                                    <li class="tier1 <% if (htmlWebpackPlugin.options.submenu == 'fmckeditor') { %>active<% } %>">
                                        <a href="#"><span class="menu-text">CKEditor</span></a>
                                        <div class="subcat">
                                            <ul>
                                                <li <% if (htmlWebpackPlugin.options.submenu2 == 'fmckeditorclassic') { %>class="active" <% } %>>
                                                    <a href="fm_ckeditor_classic.html">Classic</a>
                                                </li>
                                                <li <% if (htmlWebpackPlugin.options.submenu2 == 'fmckeditorballoon') { %>class="active" <% } %>>
                                                    <a href="fm_ckeditor_balloon.html">Balloon</a>
                                                </li>
                                                <li <% if (htmlWebpackPlugin.options.submenu2 == 'fmckeditorblock') { %>class="active" <% } %>>
                                                    <a href="fm_ckeditor_block.html">Balloon Block</a>
                                                </li>
                                                <li <% if (htmlWebpackPlugin.options.submenu2 == 'fmckeditorinline') { %>class="active" <% } %>>
                                                    <a href="fm_ckeditor_inline.html">Inline</a>
                                                </li>
                                                <li <% if (htmlWebpackPlugin.options.submenu2 == 'fmckeditordocument') { %>class="active" <% } %>>
                                                    <a href="fm_ckeditor_document.html">Document</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'ch') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-bar-chart"></i>
                                <span class="menu-text">Charts</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'chapex') { %>class="active" <% } %>>
                                        <a href="ch_apexcharts.html">Apex Charts</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'chamchart') { %>class="active" <% } %>>
                                        <a href="javascript:;">amCharts</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'chmorris') { %>class="active" <% } %>>
                                        <a href="javascript:;">Morris Charts</a>
                                    </li>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'chflot') { %>class="active" <% } %>>
                                        <a href="javascript:;">Flot Charts</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="header-menu">
                            Extra
                        </li>
                        <li class="maincat <% if (htmlWebpackPlugin.options.topmenu == 'pg') { %>active<% } %>">
                            <a href="#">
                                <i class="ti-file"></i>
                                <span class="menu-text">Pages</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li <% if (htmlWebpackPlugin.options.submenu == 'pglogin') { %>class="active" <% } %>>
                                        <a href="pg_login.html" target="_blank">Login</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="maincat">
                            <a href="#">
                                <i class="ti-layers-alt"></i>
                                <span class="menu-text">Multi-Levels Menu</span>
                            </a>
                            <div class="subcat">
                                <ul>
                                    <li class="tier1">
                                        <a href="javascript:;">Tier 1</a>
                                        <div class="subcat">
                                            <ul>
                                                <li><a href="javascript:;">Level 2</a></li>
                                                <li><a href="javascript:;">Level 2</a></li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li>
                                        <a href="javascript:;">Submenu</a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">Submenu</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li>
                            <a href="javascript:;">
                                <i class="ti-agenda"></i>
                                <span class="menu-text">Documentation</span>
                                <span class="badge badge-pill badge-primary">Beta</span>
                            </a>
                        </li>
                        <li>
                            <a href="javascript:;">
                                <i class="ti-calendar"></i>
                                <span class="menu-text">Calendar</span>
                            </a>
                        </li>
                        <li>
                            <a href="https://github.com/siQuang/siqtheme" target="_blank">
                                <i class="ti-github"></i>
                                <span class="menu-text">Github/siQtheme</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- sidebar-menu  -->
            </div>
        </div>