// ---------------------------------------------
// Grid Layout
// ---------------------------------------------
$grid-wrapper-x-margin: 	16px !default;
$grid-wrapper-y-margin: 	0 !default;
$header-height: 			74px !default;
$header-width: 				calc(100% - 32px) !default; // For fixed header
$sidebar-width: 			260px !default;
$sidebar-collapsed-width: 	60px !default;
$main-body-radius: 			5px !default;
$footer-height: 			42px !default;
$sidebar-right-width: 		360px !default;
$sidebar-right-offset: 		-370px !default;

// ---------------------------------------------
// Screen Size
// ---------------------------------------------
$screen-xs-min: 481px !default;
$screen-sm-min: 769px !default;
$screen-md-min: 993px !default;
$screen-lg-min: 1201px !default;

$screen-xs-max: ($screen-xs-min - 1) !default;
$screen-sm-max: ($screen-sm-min - 1) !default;
$screen-md-max: ($screen-md-min - 1) !default;
$screen-lg-max: ($screen-lg-min - 1) !default;

// ---------------------------------------------
// z-indexs
// ---------------------------------------------
$zindex-sidebar: 		10 !default; 
$zindex-sidebar-right: 	11 !default; 
$zindex-sidebar-slide: 	12 !default; 
$zindex-note-editor: 	20 !default; 
$zindex-header: 		21 !default;
$zindex-datepicker:     22 !default;

// ---------------------------------------------
// Default Theme
// ---------------------------------------------
// Body
$default-font-color: 						$color-gray-600 !default;
$default-background-color: 					$color-gray-800 !default;
$default-main-bg-color: 					$color-gray-100 !default;

// Header
$default-header-font-color: 				$color-gray-400 !default;
$default-header-bg-color: 					$color-gray-800 !default;
$default-header-border-color: 				$color-gray-700 !default;

// Sidebar
$default-sidebar-font-color: 				$color-gray-600 !default;
$default-sidebar-bg-color: 					$color-gray-200 !default;
$default-sidebar-header-menu-color: 		$color-gray-800 !default;

// Sidebar Collapsed
$default-collapsed-active-font-color: 		$color-gray-800 !default;
$default-collapsed-active-bg-color: 		darken($default-sidebar-bg-color, 4%) !default;

// Sidebar Right
$default-sidebar-right-tabs-font: 			$color-gray-200 !default;
$default-sidebar-right-pane-header-font: 	$color-gray-500 !default;
$default-sidebar-right-tabs-bg: 			$color-gray-600 !default;
$default-sidebar-right-tab-active-bg: 		$color-gray-800 !default;
$default-sidebar-right-bg-color: 			$color-white !default;
$default-sidebar-right-box-shadow: 			-10px 10px 10px -11px $color-gray-500 !default;

// Footer
$default-footer-font-color: 				$color-gray-600 !default;
$default-footer-bg-color: 					$color-gray-800 !default;

// Breadcrumb
$default-breadcrumb-border-color: 			$color-gray-700 !default;

// Pages
$default-card-header-font: 					$blue !default;
$default-card-bg: 							$color-white !default;
$default-card-tabs-active: 					$color-gray-100 !default;

// ---------------------------------------------
// Dark Theme
// ---------------------------------------------
// Body
$dark-font-color: 							$color-gray-500 !default;
$dark-background-color: 					darken($color-gray-900, 4%) !default;
$dark-main-bg-color: 						lighten($color-gray-900, 4%) !default;

// Header
$dark-header-font-color: 					$color-gray-400 !default;
$dark-header-bg-color: 						$dark-background-color !default;
$dark-header-border-color: 					lighten($dark-header-bg-color, 7%) !default;

// Sidebar
$dark-sidebar-font-color: 					$color-gray-500 !default;
$dark-sidebar-bg-color: 					$color-gray-900 !default;
$dark-sidebar-header-menu-color: 			$color-gray-300 !default;

// Sidebar Collapsed
$dark-collapsed-active-font-color: 			$color-gray-500 !default;
$dark-collapsed-active-bg-color: 			darken($dark-sidebar-bg-color, 2%) !default;

// Sidebar Right
$dark-sidebar-right-tabs-font: 				$color-gray-200 !default;
$dark-sidebar-right-pane-header-font: 		$color-gray-500 !default;
$dark-sidebar-right-tabs-bg: 				$color-gray-600 !default;
$dark-sidebar-right-tab-active-bg: 			$color-gray-800 !default;
$dark-sidebar-right-bg-color: 				$color-gray-900 !default;
$dark-sidebar-right-box-shadow: 			none !default;

// Footer
$dark-footer-font-color: 					$color-gray-600 !default;
$dark-footer-bg-color: 						$dark-background-color !default;

// Breadcrumb
$dark-breadcrumb-border-color: 				$color-gray-700 !default;

// Pages
$dark-card-header-font: 					$blue !default;
$dark-card-bg: 								$color-gray-900 !default;
$dark-card-tabs-active: 					$color-gray-800 !default;

// ---------------------------------------------
// Blue Theme
// ---------------------------------------------
// Body
$blue-font-color: 							$color-gray-600 !default;
$blue-background-color: 					darken($blue, 10%) !default;
$blue-main-bg-color: 						$color-gray-100 !default;

// Header
$blue-header-font-color: 					$color-gray-400 !default;
$blue-header-bg-color: 						$blue-background-color !default;
$blue-header-border-color: 					lighten($blue-header-bg-color, 7%) !default;

// Sidebar
$blue-sidebar-font-color: 					$color-gray-300 !default;
$blue-sidebar-bg-color: 					$blue !default;
$blue-sidebar-header-menu-color: 			$color-gray-300 !default;

// Sidebar Collapsed
$blue-collapsed-active-font-color: 			$color-gray-500 !default;
$blue-collapsed-active-bg-color: 			darken($blue-sidebar-bg-color, 3%) !default;

// Sidebar Right
$blue-sidebar-right-tabs-font: 				$color-gray-200 !default;
$blue-sidebar-right-pane-header-font: 		$color-gray-200 !default;
$blue-sidebar-right-tabs-bg: 				darken($blue-background-color, 2%) !default;
$blue-sidebar-right-tab-active-bg: 			darken($blue-background-color, 5%) !default;
$blue-sidebar-right-bg-color: 				$color-gray-100 !default;
$blue-sidebar-right-box-shadow: 			-10px 10px 10px -11px $color-gray-500 !default;

// Footer
$blue-footer-font-color: 					$color-gray-600 !default;
$blue-footer-bg-color: 						$blue-background-color !default;

// Breadcrumb
$blue-breadcrumb-border-color: 				$color-gray-700 !default;

// Pages
$blue-card-header-font: 					$blue !default;
$blue-card-bg: 								$color-white !default;
$blue-card-tabs-active: 					$color-gray-100 !default;
