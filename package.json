{"name": "siqtheme", "version": "1.0.0-rc.2", "author": "<PERSON><PERSON><PERSON> (<PERSON>)", "license": "MIT", "homepage": "https://github.com/siQuang/siqtheme", "description": "Reponsive Admin Template with jQuery and Bootstrap 4", "main": "dist/js/siqtheme.js", "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "dist": "cross-env MIX_BUILD=dist npm run production"}, "keywords": ["admin", "template", "dashboard", "responsive", "bootstrap", "webpack", "laravel-mix", "sass", "theme", "j<PERSON>y"], "repository": {"type": "git", "url": "git://github.com/siQuang/siqtheme.git"}, "bugs": {"url": "https://github.com/siQuang/siqtheme/issues"}, "devDependencies": {"browser-sync": "^2.26.7", "browser-sync-webpack-plugin": "^2.0.1", "cross-env": "^7.0.2", "ejs-compiled-loader": "^1.1.0", "filename-regex": "^2.0.1", "html-webpack-plugin": "^4.2.0", "laravel-mix": "^5.0.4", "resolve-url-loader": "^3.1.0", "sass": "^1.26.3", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11", "webpack": "^4.42.1"}, "dependencies": {"@ckeditor/ckeditor5-build-balloon": "^18.0.0", "@ckeditor/ckeditor5-build-balloon-block": "^18.0.0", "@ckeditor/ckeditor5-build-classic": "^18.0.0", "@ckeditor/ckeditor5-build-decoupled-document": "^18.0.0", "@ckeditor/ckeditor5-build-inline": "^18.0.0", "apexcharts": "^3.18.1", "bootstrap": "^4.4.1", "bootstrap-datepicker": "^1.9.0", "bootstrap-select": "^1.13.14", "bootstrap-timepicker": "^0.5.2", "bootstrap4-toggle": "^3.6.1", "datatables.net-bs4": "^1.10.20", "datatables.net-responsive-bs4": "^2.2.3", "font-awesome": "^4.7.0", "jquery": "^3.5.0", "moment": "^2.24.0", "peity": "^3.3.0", "popper.js": "^1.16.1", "themify-icons-scss": "^1.0.0", "toastr": "^2.1.4"}}