<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' name='viewport' />
    <meta name="viewport" content="width=device-width" />

    <title><%= htmlWebpackPlugin.options.title %></title>

    <% include src/partials/styles.ejs %>
    <link rel="stylesheet" href="assets/vendors/bootstrap4-toggle/bootstrap4-toggle.min.css">
</head>

<body class="theme-dark">
    <div class="grid-wrapper sidebar-bg bg1">

        <!-- BOF HEADER -->
        <% include src/partials/header.ejs %>
        <!-- EOF HEADER -->

        <!-- BOF ASIDE-LEFT -->
        <% include src/partials/asideleft.ejs %>
        <!-- EOF ASIDE-LEFT -->

        <!-- BOF MAIN -->
        <div class="main">

            <!-- BOF Breadcrumb -->
            <div class="row">
                <div class="col">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href=""><i class="ti-home"></i> Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="">Components</a></li>
                        <li class="breadcrumb-item active">Bootstrap Toggle</li>
                    </ol>
                </div>
            </div>
            <!-- EOF Breadcrumb -->

            <!-- BOF MAIN-BODY -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card mb-3">
                        <div class="card-header">
                            <div class="caption uppercase">
                                <i class="ti-briefcase"></i> Bootstrap 4 Toggle (Switches)
                            </div>
                            <div class="tools">
                                <a href="#" class="btn btn-outline-secondary"><i class="ti-pencil-alt"></i></a>
                                <a href="#" class="btn btn-outline-secondary"><i class="ti-settings"></i></a>
                            </div>
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <p class="card-text">Bootstrap 4 Toggle is a jQuery plugin/widget that converts plain checkboxes into
                                    responsive toggle switch buttons. Project was forked from original Bootstrap Toggle by Min Hur at <a
                                        href="http://www.bootstraptoggle.com/" target="_blank">http://www.bootstraptoggle.com/</a> </p>
                                <p class="card-text">For more information and documentation on Bootstrap 4 Toggle visit <a
                                        href="https://gitbrent.github.io/bootstrap4-toggle/"
                                        target="_blank">https://gitbrent.github.io/bootstrap4-toggle/</a></p>
                            </li>
                            <li class="list-group-item">
                                <div class="row mb-4">
                                    <div class="col-lg-6">
                                        <h5>Default Switches</h5>
                                        <p>Simply add <code>data-toggle="toggle"</code> to convert checkboxes into toggles.</p>
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="primary">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="success">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="danger">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="warning">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="info">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="light">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="dark">
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>Outline Switches</h5>
                                        <p><code>data-onstyle="outline-*"</code> and <code>data-offstyle="outline-*"</code></p>
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-secondary"
                                            data-offstyle="outline-success">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-success"
                                            data-offstyle="outline-danger">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-danger"
                                            data-offstyle="outline-warning">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-warning"
                                            data-offstyle="outline-info">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-info"
                                            data-offstyle="outline-light">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-light"
                                            data-offstyle="outline-dark">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-dark"
                                            data-offstyle="outline-primary">
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-6">
                                        <h5>Sizes</h5>
                                        <p>Apply size to switch with <code>data-size="lg"</code>, <code>data-size="sm"</code>,
                                            <code>data-size="xs"</code></p>
                                        <input type="checkbox" checked data-toggle="toggle" data-size="lg">
                                        <input type="checkbox" checked data-toggle="toggle">
                                        <input type="checkbox" checked data-toggle="toggle" data-size="sm">
                                        <input type="checkbox" checked data-toggle="toggle" data-size="xs">
                                        <input type="checkbox" checked data-toggle="toggle" data-size="lg" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-size="sm" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-size="xs" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>Custom Sizes</h5>
                                        <p>Apply custom sizes with <code>data-width</code> and <code>data-height</code> options.</p>
                                        <input type="checkbox" checked data-toggle="toggle" data-width="100" data-height="75">
                                        <input type="checkbox" data-toggle="toggle" data-height="75">
                                        <input type="checkbox" checked data-toggle="toggle" data-width="100">
                                        <input type="checkbox" checked data-toggle="toggle" data-height="55" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-width="100" data-height="75"
                                            data-onstyle="outline-primary" data-offstyle="outline-secondary">
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-6">
                                        <h5>Custom Text</h5>
                                        <p>Apply text to switch with <code>data-on</code> and <code>data-off</code></p>
                                        <input type="checkbox" checked data-toggle="toggle" data-on="Ready" data-off="Not Ready"
                                            data-onstyle="success" data-offstyle="danger">
                                        <input type="checkbox" data-toggle="toggle" data-on="True" data-off="False" data-onstyle="primary"
                                            data-offstyle="secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-on="Yes" data-off="No" data-onstyle="jade"
                                            data-offstyle="rose">
                                        <input type="checkbox" data-toggle="toggle" data-on="Correct" data-off="Incorrect"
                                            data-onstyle="info" data-offstyle="danger">
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>HTML, Icons, Images</h5>
                                        <p>You can easily add icons or images since html is supported for on/off text.</p>
                                        <input type="checkbox" checked data-toggle="toggle" data-on="<i class='fa fa-play'></i> Play"
                                            data-off="<i class='fa fa-pause'></i> Pause">
                                        <input type="checkbox" checked data-toggle="toggle" data-on="Thumbs <i class='fa fa-thumbs-up'></i>"
                                            data-off="Thumbs <i class='fa fa-thumbs-down'></i>" data-onstyle="orchid">
                                        <input type="checkbox" data-toggle="toggle" data-on="<i class='fa fa-usd'></i> USD"
                                            data-off="<i class='fa fa-eur'></i> Euro" data-onstyle="forest" data-offstyle="pumpkin">
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-6">
                                        <h5>Multiple Lines of Text</h5>
                                        <p>Toggles with multiple lines will adjust its heights.</p>
                                        <input type="checkbox" checked data-toggle="toggle" data-on="Hello<br>World"
                                            data-off="Goodbye<br>World">
                                        <input type="checkbox" checked data-toggle="toggle" data-on="I Got<br>An Idea<br>Lets Go"
                                            data-off="I Have<br>No Idea<br>Do You?" data-onstyle="outline-primary"
                                            data-offstyle="outline-secondary">
                                        <input type="checkbox" checked data-toggle="toggle" data-on="Color<br>Bubblegum"
                                            data-off="Color<br>Carolina" data-onstyle="bubblegum" data-offstyle="carolina">
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>Stacked Switches</h5>
                                        <p>Wrap checkboxes in <code>.form-check</code> for stackable switches.</p>
                                        <div class="form-check pl-0 pb-2">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" checked>
                                            <label class="form-check-label">Enabled</label>
                                        </div>
                                        <div class="form-check pl-0">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" disabled>
                                            <label class="form-check-label">Disabled Switch with <code>disabled</code> attribute</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-6">
                                        <h5>Inline Switches</h5>
                                        <p>Add the <code>.form-check-inline</code> class to the <code>.form-check</code> wrapper for inline
                                            switches. Use <code>data-style="mr-*"</code> for spacing between switch.</p>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" data-on="Yes" data-off="No"
                                                data-style="mr-1" checked>
                                            <label class="form-check-label">Over 18?</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" data-style="mr-1">
                                            <label class="form-check-label">Turn Light On?</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" data-on="Yes" data-off="No"
                                                data-style="mr-1" checked>
                                            <label class="form-check-label">I Voted</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <h5>Animation Speed</h5>
                                        <p>Apply <code>data-style="speed-slow"</code> or <code>data-style="speed-fast"</code> option for
                                            animation speed.</p>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle"
                                                data-style="speed-slow mr-1">
                                            <label class="form-check-label">Slow</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle" data-style="mr-1">
                                            <label class="form-check-label">Default</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" data-toggle="toggle"
                                                data-style="speed-fast mr-1">
                                            <label class="form-check-label">Fast</label>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <div class="card-footer">
                                <div class="row mb-4">
                                    <div class="col-lg-12">
                                        <h5>Options</h5>
                                        <p>Options can be passed via data attributes or JavaScript. For data attributes, append the option
                                            name to data-, as in data-on="Enabled".</p>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Type</th>
                                                        <th>Default</th>
                                                        <th>Description</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="text-danger">on</td>
                                                        <td>string|html</td>
                                                        <td class="text-danger">"On"</td>
                                                        <td>Text of the on toggle label.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">off</td>
                                                        <td>string|html</td>
                                                        <td class="text-danger">"Off"</td>
                                                        <td>Text of the off toggle label.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">onstyle</td>
                                                        <td>string</td>
                                                        <td class="text-danger">"primary"</td>
                                                        <td>Style of the on toggle.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">offstyle</td>
                                                        <td>string</td>
                                                        <td class="text-danger">"light"</td>
                                                        <td>Style of the off toggle.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">size</td>
                                                        <td>string</td>
                                                        <td>null</td>
                                                        <td>Size of the toggle. If set to null, button is default/normal size.<br>Possible
                                                            values are: lg, sm, xs</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">style</td>
                                                        <td>string</td>
                                                        <td>null</td>
                                                        <td>Appends the provided value to the toggle's class attribute. Use this to apply
                                                            custom styles to the toggle.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">width</td>
                                                        <td>integer</td>
                                                        <td>null</td>
                                                        <td>Sets the width of the toggle.<br>If set to null, width will be calculated.</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-danger">height</td>
                                                        <td>integer</td>
                                                        <td>null</td>
                                                        <td>Sets the height of the toggle.<br>If set to null, height will be calculated.
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-12">
                                        <h5>Methods</h5>
                                        <p>Methods can be used to control toggles directly.</p>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Method</th>
                                                        <th>Example</th>
                                                        <th>Description</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>initialize</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle()</td>
                                                        <td>Initializes the toggle plugin with options</td>
                                                    </tr>
                                                    <tr>
                                                        <td>destroy</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('destroy')</td>
                                                        <td>Destroys the toggle</td>
                                                    </tr>
                                                    <tr>
                                                        <td>on</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('on')</td>
                                                        <td>Sets the toggle to 'On' state</td>
                                                    </tr>
                                                    <tr>
                                                        <td>off</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('off')</td>
                                                        <td>Sets the toggle to 'Off' state</td>
                                                    </tr>
                                                    <tr>
                                                        <td>toggle</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('toggle')</td>
                                                        <td>Toggles the state of the toggle</td>
                                                    </tr>
                                                    <tr>
                                                        <td>enable</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('enable')</td>
                                                        <td>Enables the toggle</td>
                                                    </tr>
                                                    <tr>
                                                        <td>disable</td>
                                                        <td class="text-danger">$('#toggle-demo').bootstrapToggle('disable')</td>
                                                        <td>Disables the toggle</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-lg-12">
                                        <h5>Event Propagation</h5>
                                        <p>Note All events are propagated to and from input element to the toggle. You should listen to
                                            events from the <code>&lt;input type="checkbox"&gt;</code> directly rather than look for custom
                                            events.</p>
                    
                                        <p><code>$('#toggle-event').change(function() { //Do something } );</code></p>
                                    </div>
                                </div>
                            </div>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- EOF MAIN-BODY -->

        </div>
        <!-- EOF MAIN -->

        <!-- BOF FOOTER -->
        <% include src/partials/footer.ejs %>
        <!-- EOF FOOTER -->

        <!-- BOF ASIDE-RIGHT -->
        <% include src/partials/asideright.ejs %>
        <!-- EOF ASIDE-RIGHT -->

        <div id="overlay"></div>

    </div> <!-- END WRAPPER -->

    <% include src/partials/scripts.ejs %>
    <script src="assets/vendors/bootstrap4-toggle/bootstrap4-toggle.min.js"></script>
</body>

</html>